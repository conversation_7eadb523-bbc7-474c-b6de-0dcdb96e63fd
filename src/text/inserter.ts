import { clipboard } from 'electron';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export class TextInserter {
  private platform: string;

  constructor() {
    this.platform = process.platform;
  }

  async insertText(text: string, isReplace: boolean = false): Promise<boolean> {
    try {
      console.log(`Inserting text: "${text}"`);
      
      // Store current clipboard content
      const originalClipboard = clipboard.readText();
      
      if (isReplace) {
        await this.simulateCut();
        // Wait for the cut operation to complete before overwriting clipboard
        await this.sleep(200);
      }
      
      // Set the text to clipboard
      clipboard.writeText(text);
      
      // Simulate paste operation based on platform
      await this.simulatePaste();
      
      // Restore original clipboard content after a short delay
      setTimeout(() => {
        clipboard.writeText(originalClipboard);
      }, 1000);
      
      return true;
    } catch (error) {
      console.error('Failed to insert text:', error);
      return false;
    }
  }

  private async simulatePaste(): Promise<void> {
    try {
      switch (this.platform) {
        case 'win32':
          await this.simulatePasteWindows();
          break;
        case 'darwin':
          await this.simulatePasteMacOS();
          break;
        case 'linux':
          await this.simulatePasteLinux();
          break;
        default:
          throw new Error(`Unsupported platform: ${this.platform}`);
      }
    } catch (error) {
      console.error('Failed to simulate paste:', error);
      throw error;
    }
  }

  private async simulatePasteWindows(): Promise<void> {
    // On Windows, we can use PowerShell to send Ctrl+V
    const command = `
      Add-Type -AssemblyName System.Windows.Forms
      [System.Windows.Forms.SendKeys]::SendWait("^v")
    `;
    
    await execAsync(`powershell -Command "${command}"`);
  }

  private async simulatePasteMacOS(): Promise<void> {
    // On macOS, we can use osascript to send Cmd+V
    const command = `osascript -e 'tell application "System Events" to keystroke "v" using command down'`;
    await execAsync(command);
  }

  private async simulatePasteLinux(): Promise<void> {
    // On Linux, we can use xdotool to send Ctrl+V
    // First check if xdotool is available
    try {
      await execAsync('which xdotool');
      await execAsync('xdotool key ctrl+v');
    } catch (error) {
      // Fallback to xclip + xdotool alternative
      try {
        await execAsync('which xclip');
        await execAsync('xdotool key ctrl+v');
      } catch (fallbackError) {
        console.warn('Neither xdotool nor xclip found. Text insertion may not work properly.');
        throw new Error('Required tools (xdotool or xclip) not found for text insertion on Linux');
      }
    }
  }

  private async simulateCut(): Promise<void> {
    try {
      switch (this.platform) {
        case 'win32': {
          // On Windows, use PowerShell to send Ctrl+X
          const command = `
            Add-Type -AssemblyName System.Windows.Forms
            [System.Windows.Forms.SendKeys]::SendWait("^x")
          `;
          
          await execAsync(`powershell -Command "${command}"`);
          break;
        }
        
        case 'darwin': {
          // On macOS, use osascript to send Cmd+X
          const command = `osascript -e 'tell application "System Events" to keystroke "x" using command down'`;
          await execAsync(command);
          break;
        }
        
        case 'linux': {
          // On Linux, use xdotool to send Ctrl+X
          // First check if xdotool is available
          try {
            await execAsync('which xdotool');
            await execAsync('xdotool key ctrl+x');
          } catch (error) {
            // Fallback to xclip + xdotool alternative
            try {
              await execAsync('which xclip');
              await execAsync('xdotool key ctrl+x');
            } catch (fallbackError) {
              console.warn('Neither xdotool nor xclip found. Text cutting may not work properly.');
              throw new Error('Required tools (xdotool or xclip) not found for text cutting on Linux');
            }
          }
          break;
        }
        
        default:
          throw new Error(`Unsupported platform: ${this.platform}`);
      }
    } catch (error) {
      console.error('Failed to simulate cut:', error);
      throw error;
    }
  }

  // Alternative method using robotjs (when available)
  async insertTextWithRobotJS(text: string): Promise<boolean> {
    try {
      // This would require robotjs to be properly installed
      // For now, we'll use the clipboard method as fallback
      console.log('RobotJS not available, using clipboard method');
      return await this.insertText(text);
    } catch (error) {
      console.error('RobotJS text insertion failed:', error);
      return false;
    }
  }

  // Method to type text character by character (slower but more reliable)
  async typeText(text: string, delay: number = 50): Promise<boolean> {
    try {
      for (const char of text) {
        await this.typeCharacter(char);
        if (delay > 0) {
          await this.sleep(delay);
        }
      }
      return true;
    } catch (error) {
      console.error('Failed to type text:', error);
      return false;
    }
  }

  private async typeCharacter(char: string): Promise<void> {
    // Store and restore clipboard for each character
    const originalClipboard = clipboard.readText();
    clipboard.writeText(char);
    await this.simulatePaste();
    clipboard.writeText(originalClipboard);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Method to check if text insertion is supported on current platform
  async isSupported(): Promise<boolean> {
    try {
      switch (this.platform) {
        case 'win32':
          // Check if PowerShell is available
          await execAsync('powershell -Command "Get-Host"');
          return true;
          
        case 'darwin':
          // Check if osascript is available
          await execAsync('which osascript');
          return true;
          
        case 'linux':
          // Check if xdotool is available
          try {
            await execAsync('which xdotool');
            return true;
          } catch {
            // Check for xclip as fallback
            await execAsync('which xclip');
            return true;
          }
          
        default:
          return false;
      }
    } catch (error) {
      console.warn('Text insertion support check failed:', error);
      return false;
    }
  }

  // Method to install required dependencies on Linux
  async installLinuxDependencies(): Promise<string> {
    if (this.platform !== 'linux') {
      return 'Not applicable on this platform';
    }

    const instructions = `
To enable text insertion on Linux, please install one of the following:

Option 1 - xdotool (recommended):
  Ubuntu/Debian: sudo apt-get install xdotool
  Fedora/RHEL:   sudo dnf install xdotool
  Arch:          sudo pacman -S xdotool

Option 2 - xclip:
  Ubuntu/Debian: sudo apt-get install xclip
  Fedora/RHEL:   sudo dnf install xclip
  Arch:          sudo pacman -S xclip

After installation, restart the application.
    `;

    return instructions.trim();
  }

  // Method to get platform-specific requirements
  getPlatformRequirements(): string {
    switch (this.platform) {
      case 'win32':
        return 'Windows: PowerShell (built-in)';
      case 'darwin':
        return 'macOS: osascript (built-in)';
      case 'linux':
        return 'Linux: xdotool or xclip (install via package manager)';
      default:
        return 'Platform not supported';
    }
  }
}

// Text insertion implementation notes:
//
// This implementation uses a clipboard-based approach which is:
// 1. Cross-platform compatible
// 2. Doesn't require additional native dependencies
// 3. Works with most applications
//
// For a more robust implementation, consider:
//
// 1. Using robotjs for direct keyboard simulation:
//    - More reliable than clipboard method
//    - Requires native compilation
//    - May need additional system permissions
//
// 2. Platform-specific solutions:
//    - Windows: SendInput API via native addon
//    - macOS: CGEventCreateKeyboardEvent via native addon
//    - Linux: X11 or Wayland APIs via native addon
//
// 3. Accessibility APIs:
//    - Windows: UI Automation
//    - macOS: Accessibility API
//    - Linux: AT-SPI
//
// 4. Security considerations:
//    - Some applications may block programmatic input
//    - Antivirus software may flag keyboard simulation
//    - User permissions may be required for accessibility features
