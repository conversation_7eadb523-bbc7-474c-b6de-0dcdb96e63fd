import { createClient, SupabaseClient, User, Session } from '@supabase/supabase-js';

export interface AuthConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  backendUrl: string;
}

export interface AuthUser {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}

export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: AuthUser;
}

export interface AuthResponse {
  success: boolean;
  data?: {
    user?: AuthUser;
    session?: AuthSession;
  };
  error?: string;
}

export class AuthService {
  private supabase: SupabaseClient;
  private backendUrl: string;
  private currentSession: AuthSession | null = null;
  private authStateListeners: Array<(session: AuthSession | null) => void> = [];

  constructor(config: AuthConfig) {
    this.supabase = createClient(config.supabaseUrl, config.supabaseAnonKey);
    this.backendUrl = config.backendUrl;
    
    // Listen for auth state changes
    this.supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event, session);
      
      if (session) {
        this.currentSession = {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          expires_at: session.expires_at || 0,
          user: {
            id: session.user.id,
            email: session.user.email || '',
            created_at: session.user.created_at,
            updated_at: session.user.updated_at || session.user.created_at,
          },
        };
      } else {
        this.currentSession = null;
      }
      
      // Notify listeners
      this.authStateListeners.forEach(listener => listener(this.currentSession));
    });
  }

  // Sign up new user
  async signUp(email: string, password: string): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        console.error('Sign up error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        data: {
          user: data.user ? {
            id: data.user.id,
            email: data.user.email || '',
            created_at: data.user.created_at,
            updated_at: data.user.updated_at || data.user.created_at,
          } : undefined,
          session: data.session ? {
            access_token: data.session.access_token,
            refresh_token: data.session.refresh_token,
            expires_at: data.session.expires_at || 0,
            user: {
              id: data.session.user.id,
              email: data.session.user.email || '',
              created_at: data.session.user.created_at,
              updated_at: data.session.user.updated_at || data.session.user.created_at,
            },
          } : undefined,
        },
      };
    } catch (error: any) {
      console.error('Sign up failed:', error);
      return {
        success: false,
        error: error.message || 'Sign up failed',
      };
    }
  }

  // Sign in user
  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Sign in error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        data: {
          user: data.user ? {
            id: data.user.id,
            email: data.user.email || '',
            created_at: data.user.created_at,
            updated_at: data.user.updated_at || data.user.created_at,
          } : undefined,
          session: data.session ? {
            access_token: data.session.access_token,
            refresh_token: data.session.refresh_token,
            expires_at: data.session.expires_at || 0,
            user: {
              id: data.session.user.id,
              email: data.session.user.email || '',
              created_at: data.session.user.created_at,
              updated_at: data.session.user.updated_at || data.session.user.created_at,
            },
          } : undefined,
        },
      };
    } catch (error: any) {
      console.error('Sign in failed:', error);
      return {
        success: false,
        error: error.message || 'Sign in failed',
      };
    }
  }

  // Sign out user
  async signOut(): Promise<AuthResponse> {
    try {
      const { error } = await this.supabase.auth.signOut();

      if (error) {
        console.error('Sign out error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      this.currentSession = null;
      return {
        success: true,
      };
    } catch (error: any) {
      console.error('Sign out failed:', error);
      return {
        success: false,
        error: error.message || 'Sign out failed',
      };
    }
  }

  // Get current session
  getCurrentSession(): AuthSession | null {
    return this.currentSession;
  }

  // Get current user
  getCurrentUser(): AuthUser | null {
    return this.currentSession?.user || null;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.currentSession !== null;
  }

  // Get access token for API requests
  getAccessToken(): string | null {
    return this.currentSession?.access_token || null;
  }

  // Add auth state listener
  onAuthStateChange(listener: (session: AuthSession | null) => void): () => void {
    this.authStateListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(listener);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  // Initialize session from stored state
  async initializeSession(): Promise<void> {
    try {
      const { data: { session }, error } = await this.supabase.auth.getSession();
      
      if (error) {
        console.error('Failed to get session:', error);
        return;
      }

      if (session) {
        this.currentSession = {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          expires_at: session.expires_at || 0,
          user: {
            id: session.user.id,
            email: session.user.email || '',
            created_at: session.user.created_at,
            updated_at: session.user.updated_at || session.user.created_at,
          },
        };
        
        console.log('Session initialized:', this.currentSession.user.email);
      }
    } catch (error) {
      console.error('Failed to initialize session:', error);
    }
  }

  // Reset password
  async resetPassword(email: string): Promise<AuthResponse> {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(email);

      if (error) {
        console.error('Reset password error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
      };
    } catch (error: any) {
      console.error('Reset password failed:', error);
      return {
        success: false,
        error: error.message || 'Reset password failed',
      };
    }
  }
}
