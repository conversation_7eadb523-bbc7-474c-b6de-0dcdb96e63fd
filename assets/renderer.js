const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class RendererApp {
    constructor() {
        this.isRecording = false;
        this.config = null;
        this.currentUser = null;
        this.isAuthenticated = false;
        this.initializeElements();
        this.setupEventListeners();
        this.loadConfiguration();
        this.checkAuthState();
    }

    initializeElements() {
        // Status elements
        this.statusDot = document.getElementById('statusDot');
        this.statusText = document.getElementById('statusText');
        
        // Control elements
        this.recordBtn = document.getElementById('recordBtn');
        this.settingsBtn = document.getElementById('settingsBtn');
        this.saveConfigBtn = document.getElementById('saveConfigBtn');
        
        // Authentication elements
        this.authSection = document.getElementById('authSection');
        this.authTitle = document.getElementById('authTitle');
        this.signInForm = document.getElementById('signInForm');
        this.signUpForm = document.getElementById('signUpForm');
        this.userInfo = document.getElementById('userInfo');

        // Sign in form elements
        this.signInEmail = document.getElementById('signInEmail');
        this.signInPassword = document.getElementById('signInPassword');
        this.signInBtn = document.getElementById('signInBtn');
        this.showSignUpBtn = document.getElementById('showSignUpBtn');
        this.forgotPasswordBtn = document.getElementById('forgotPasswordBtn');

        // Sign up form elements
        this.signUpEmail = document.getElementById('signUpEmail');
        this.signUpPassword = document.getElementById('signUpPassword');
        this.signUpPasswordConfirm = document.getElementById('signUpPasswordConfirm');
        this.signUpBtn = document.getElementById('signUpBtn');
        this.showSignInBtn = document.getElementById('showSignInBtn');

        // User info elements
        this.userEmail = document.getElementById('userEmail');
        this.signOutBtn = document.getElementById('signOutBtn');
        this.viewUsageBtn = document.getElementById('viewUsageBtn');

        // Configuration elements
        this.configSection = document.getElementById('configSection');
        this.languageSelect = document.getElementById('language');
        this.hotkeyDisplay = document.getElementById('hotkeyDisplay');
        this.hotkeyValidation = document.getElementById('hotkeyValidation');
        this.hotkeySuggestions = document.getElementById('hotkeysuggestions');
        this.resetHotkeyBtn = document.getElementById('resetHotkeyBtn');

        // Transcription elements
        this.transcriptionText = document.getElementById('transcriptionText');

        // Error/success elements
        this.authError = document.getElementById('authError');
        this.authSuccess = document.getElementById('authSuccess');
        
        // Hotkey configuration state
        this.isCapturingHotkey = false;
        this.capturedKeys = new Set();
        this.hotkeyTimeout = null;
        this.supportedKeys = null;
    }

    setupEventListeners() {
        // Button event listeners
        this.recordBtn.addEventListener('click', () => this.toggleRecording());
        this.settingsBtn.addEventListener('click', () => this.toggleSettings());
        this.saveConfigBtn.addEventListener('click', () => this.saveConfiguration());

        // Authentication event listeners
        this.signInBtn.addEventListener('click', () => this.signIn());
        this.signUpBtn.addEventListener('click', () => this.signUp());
        this.signOutBtn.addEventListener('click', () => this.signOut());
        this.showSignUpBtn.addEventListener('click', () => this.showSignUpForm());
        this.showSignInBtn.addEventListener('click', () => this.showSignInForm());
        this.forgotPasswordBtn.addEventListener('click', () => this.resetPassword());
        this.viewUsageBtn.addEventListener('click', () => this.showUsageStats());

        // Enter key handling for auth forms
        this.signInEmail.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.signIn();
        });
        this.signInPassword.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.signIn();
        });
        this.signUpPasswordConfirm.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.signUp();
        });
        
        // Hotkey configuration
        this.hotkeyDisplay.addEventListener('click', () => this.startHotkeyCapture());
        this.resetHotkeyBtn.addEventListener('click', () => this.resetHotkeyToDefault());
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // IPC event listeners from main process
        ipcRenderer.on('recording-started', () => this.onRecordingStarted());
        ipcRenderer.on('recording-stopped', () => this.onRecordingStopped());
        ipcRenderer.on('transcription-complete', (event, text) => this.onTranscriptionComplete(text));
        ipcRenderer.on('ai-response-complete', (event, data) => this.onAIResponseComplete(data));
        ipcRenderer.on('hotkey-fallback-used', (event, data) => this.onHotkeyFallbackUsed(data));
        ipcRenderer.on('hotkey-registration-failed', (event, data) => this.onHotkeyRegistrationFailed(data));
        ipcRenderer.on('auth-state-changed', (event, data) => this.onAuthStateChanged(data));
        
        // Window focus events
        window.addEventListener('focus', () => this.onWindowFocus());
        window.addEventListener('blur', () => this.onWindowBlur());
    }

    async loadConfiguration() {
        try {
            this.config = await ipcRenderer.invoke('get-config');
            this.supportedKeys = await ipcRenderer.invoke('get-supported-keys');
            this.updateUIFromConfig();
            this.setupHotkeySuggestions();
        } catch (error) {
            console.error('Failed to load configuration:', error);
            this.showError('Failed to load configuration');
        }
    }

    // Authentication methods
    async checkAuthState() {
        try {
            const user = await ipcRenderer.invoke('auth-get-current-user');
            if (user) {
                this.currentUser = user;
                this.isAuthenticated = true;
                this.updateAuthUI();
            } else {
                this.currentUser = null;
                this.isAuthenticated = false;
                this.updateAuthUI();
            }
        } catch (error) {
            console.error('Failed to check auth state:', error);
            this.currentUser = null;
            this.isAuthenticated = false;
            this.updateAuthUI();
        }
    }

    async signIn() {
        const email = this.signInEmail.value.trim();
        const password = this.signInPassword.value;

        if (!email || !password) {
            this.showAuthError('Please enter both email and password');
            return;
        }

        try {
            this.signInBtn.disabled = true;
            this.signInBtn.textContent = 'Signing in...';

            const result = await ipcRenderer.invoke('auth-sign-in', email, password);

            if (result.success) {
                this.currentUser = result.data.user;
                this.isAuthenticated = true;
                this.showAuthSuccess('Signed in successfully!');
                this.clearAuthForms();
                this.updateAuthUI();
            } else {
                this.showAuthError(result.error || 'Sign in failed');
            }
        } catch (error) {
            console.error('Sign in error:', error);
            this.showAuthError('Sign in failed. Please try again.');
        } finally {
            this.signInBtn.disabled = false;
            this.signInBtn.textContent = 'Sign In';
        }
    }

    async signUp() {
        const email = this.signUpEmail.value.trim();
        const password = this.signUpPassword.value;
        const confirmPassword = this.signUpPasswordConfirm.value;

        if (!email || !password || !confirmPassword) {
            this.showAuthError('Please fill in all fields');
            return;
        }

        if (password !== confirmPassword) {
            this.showAuthError('Passwords do not match');
            return;
        }

        if (password.length < 6) {
            this.showAuthError('Password must be at least 6 characters long');
            return;
        }

        try {
            this.signUpBtn.disabled = true;
            this.signUpBtn.textContent = 'Creating account...';

            const result = await ipcRenderer.invoke('auth-sign-up', email, password);

            if (result.success) {
                this.showAuthSuccess('Account created successfully! Please sign in.');
                this.clearAuthForms();
                this.showSignInForm();
            } else {
                this.showAuthError(result.error || 'Account creation failed');
            }
        } catch (error) {
            console.error('Sign up error:', error);
            this.showAuthError('Account creation failed. Please try again.');
        } finally {
            this.signUpBtn.disabled = false;
            this.signUpBtn.textContent = 'Create Account';
        }
    }

    async signOut() {
        try {
            this.signOutBtn.disabled = true;
            this.signOutBtn.textContent = 'Signing out...';

            const result = await ipcRenderer.invoke('auth-sign-out');

            if (result.success) {
                this.currentUser = null;
                this.isAuthenticated = false;
                this.showAuthSuccess('Signed out successfully');
                this.clearAuthForms();
                this.updateAuthUI();
            } else {
                this.showAuthError(result.error || 'Sign out failed');
            }
        } catch (error) {
            console.error('Sign out error:', error);
            this.showAuthError('Sign out failed. Please try again.');
        } finally {
            this.signOutBtn.disabled = false;
            this.signOutBtn.textContent = 'Sign Out';
        }
    }

    async resetPassword() {
        const email = this.signInEmail.value.trim();

        if (!email) {
            this.showAuthError('Please enter your email address first');
            return;
        }

        try {
            const result = await ipcRenderer.invoke('auth-reset-password', email);

            if (result.success) {
                this.showAuthSuccess('Password reset email sent! Check your inbox.');
            } else {
                this.showAuthError(result.error || 'Password reset failed');
            }
        } catch (error) {
            console.error('Password reset error:', error);
            this.showAuthError('Password reset failed. Please try again.');
        }
    }

    showSignInForm() {
        this.authTitle.textContent = 'Sign In';
        this.signInForm.classList.remove('hidden');
        this.signUpForm.classList.add('hidden');
        this.clearAuthMessages();
    }

    showSignUpForm() {
        this.authTitle.textContent = 'Create Account';
        this.signInForm.classList.add('hidden');
        this.signUpForm.classList.remove('hidden');
        this.clearAuthMessages();
    }

    updateAuthUI() {
        if (this.isAuthenticated && this.currentUser) {
            // Show user info, hide auth forms
            this.signInForm.classList.add('hidden');
            this.signUpForm.classList.add('hidden');
            this.userInfo.classList.remove('hidden');
            this.userEmail.textContent = this.currentUser.email;

            // Update status
            this.updateStatus('Ready', 'ready');
        } else {
            // Show sign in form, hide user info
            this.userInfo.classList.add('hidden');
            this.showSignInForm();

            // Update status
            this.updateStatus('Sign in required', 'error');
        }
    }

    onAuthStateChanged(data) {
        this.isAuthenticated = data.authenticated;
        this.currentUser = data.user;
        this.updateAuthUI();

        if (data.authenticated) {
            this.showAuthSuccess(`Welcome back, ${data.user.email}!`);
        }
    }

    clearAuthForms() {
        this.signInEmail.value = '';
        this.signInPassword.value = '';
        this.signUpEmail.value = '';
        this.signUpPassword.value = '';
        this.signUpPasswordConfirm.value = '';
    }

    clearAuthMessages() {
        this.authError.classList.add('hidden');
        this.authSuccess.classList.add('hidden');
    }

    showAuthError(message) {
        this.authError.textContent = message;
        this.authError.classList.remove('hidden');
        this.authSuccess.classList.add('hidden');
    }

    showAuthSuccess(message) {
        this.authSuccess.textContent = message;
        this.authSuccess.classList.remove('hidden');
        this.authError.classList.add('hidden');
    }

    async showUsageStats() {
        try {
            const stats = await ipcRenderer.invoke('get-usage-stats', 30);

            const message = `Usage Stats (Last 30 days):
• Total Requests: ${stats.totalRequests}
• Total Tokens: ${stats.totalTokens}
• Audio Duration: ${Math.round(stats.totalAudioDuration)} seconds
• Transcriptions: ${stats.requestsByEndpoint.transcribe || 0}
• AI Requests: ${stats.requestsByEndpoint['process-ai'] || 0}`;

            this.transcriptionText.textContent = message;
        } catch (error) {
            console.error('Failed to get usage stats:', error);
            this.showAuthError('Failed to load usage statistics');
        }
    }

    updateUIFromConfig() {
        if (!this.config) return;

        // Update language
        this.languageSelect.value = this.config.speechSettings.language || 'en';

        // Update hotkey display
        this.updateHotkeyDisplay(this.config.hotkey || 'CommandOrControl+Shift+Space');

        // Hide settings by default
        this.configSection.style.display = 'none';
        this.authSection.style.display = 'none';
    }

    async updateHotkeyDisplay(hotkey) {
        try {
            const displayText = await ipcRenderer.invoke('format-hotkey', hotkey);
            this.hotkeyDisplay.textContent = displayText || hotkey || 'Ctrl/Cmd + Shift + Space';
            this.hotkeyDisplay.className = 'hotkey-display';
            this.clearHotkeyValidation();
        } catch (error) {
            console.error('Failed to format hotkey:', error);
            this.hotkeyDisplay.textContent = hotkey || 'Ctrl/Cmd + Shift + Space';
        }
    }

    setupHotkeySuggestions() {
        if (!this.hotkeySuggestions) return;

        // Clear existing suggestions
        const existingSuggestions = this.hotkeySuggestions.querySelectorAll('.hotkey-suggestion');
        existingSuggestions.forEach(el => el.remove());

        // Add default suggestions
        const suggestions = [
            'CommandOrControl+Shift+Space',
            'CommandOrControl+Alt+Space',
            'F2',
            'F3',
            'Alt+Space'
        ];

        suggestions.forEach(suggestion => {
            const suggestionEl = document.createElement('div');
            suggestionEl.className = 'hotkey-suggestion';
            suggestionEl.textContent = suggestion.replace('CommandOrControl', 'Ctrl/Cmd');
            suggestionEl.addEventListener('click', () => this.applySuggestedHotkey(suggestion));
            this.hotkeySuggestions.appendChild(suggestionEl);
        });
    }

    async applySuggestedHotkey(hotkey) {
        try {
            // Test the hotkey first
            const testResult = await ipcRenderer.invoke('test-hotkey', hotkey);
            if (!testResult.available) {
                this.showHotkeyValidation(testResult.error || 'Hotkey not available', 'error');
                return;
            }

            // Apply the hotkey
            const result = await ipcRenderer.invoke('update-hotkey', hotkey);
            if (result.success) {
                this.config.hotkey = hotkey;
                this.updateHotkeyDisplay(hotkey);
                this.showHotkeyValidation('Hotkey updated successfully!', 'success');
            } else {
                this.showHotkeyValidation(result.error || 'Failed to update hotkey', 'error');
            }
        } catch (error) {
            console.error('Failed to apply suggested hotkey:', error);
            this.showHotkeyValidation('Failed to apply hotkey', 'error');
        }
    }

    startHotkeyCapture() {
        if (this.isCapturingHotkey) return;

        this.isCapturingHotkey = true;
        this.capturedKeys.clear();
        this.hotkeyDisplay.textContent = 'Press your desired key combination...';
        this.hotkeyDisplay.className = 'hotkey-display capturing';
        this.clearHotkeyValidation();

        // Auto-cancel after 10 seconds
        this.hotkeyTimeout = setTimeout(() => {
            if (this.isCapturingHotkey) {
                this.cancelHotkeyCapture();
            }
        }, 10000);
    }

    handleKeyDown(event) {
        if (!this.isCapturingHotkey) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        // Add key to captured keys
        if (event.ctrlKey) this.capturedKeys.add('ctrl');
        if (event.shiftKey) this.capturedKeys.add('shift');
        if (event.altKey) this.capturedKeys.add('alt');
        if (event.metaKey) this.capturedKeys.add('cmd');
        
        // Add the main key (not modifier keys)
        if (!['Control', 'Shift', 'Alt', 'Meta'].includes(event.key)) {
            // Map special keys to their proper names
            let keyName = event.key;
            if (keyName === ' ') keyName = 'space';
            else if (keyName === 'Enter') keyName = 'return';
            else if (keyName === 'Escape') keyName = 'escape';
            else if (keyName === 'Tab') keyName = 'tab';
            else if (keyName === 'Backspace') keyName = 'backspace';
            else if (keyName === 'Delete') keyName = 'delete';
            else keyName = keyName.toLowerCase();
            
            this.capturedKeys.add(keyName);
        }
        
        this.updateHotkeyPreview();
    }

    handleKeyUp(event) {
        if (!this.isCapturingHotkey) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        // If this was a modifier key release and we have a complete combination, save it
        if (['Control', 'Shift', 'Alt', 'Meta'].includes(event.key) && this.capturedKeys.size >= 2) {
            this.saveHotkey();
        }
    }

    updateHotkeyPreview() {
        const keys = Array.from(this.capturedKeys);
        const displayKeys = [];
        
        // Order modifiers first
        if (keys.includes('ctrl')) displayKeys.push('Ctrl');
        if (keys.includes('cmd')) displayKeys.push('Cmd');
        if (keys.includes('shift')) displayKeys.push('Shift');
        if (keys.includes('alt')) displayKeys.push('Alt');
        
        // Add main key
        const mainKey = keys.find(k => !['ctrl', 'cmd', 'shift', 'alt'].includes(k));
        if (mainKey) {
            // Format special keys for display
            let displayKey = mainKey;
            if (mainKey === 'space') displayKey = 'Space';
            else if (mainKey === 'return') displayKey = 'Enter';
            else if (mainKey === 'escape') displayKey = 'Esc';
            else if (mainKey === 'tab') displayKey = 'Tab';
            else if (mainKey === 'backspace') displayKey = 'Backspace';
            else if (mainKey === 'delete') displayKey = 'Delete';
            else displayKey = mainKey.toUpperCase();
            
            displayKeys.push(displayKey);
        }
        
        this.hotkeyDisplay.textContent = displayKeys.join(' + ') || 'Press keys...';
    }

    async saveHotkey() {
        const keys = Array.from(this.capturedKeys);

        // Convert to Electron format
        let electronKeys = [];

        if (keys.includes('ctrl') || keys.includes('cmd')) {
            electronKeys.push('CommandOrControl');
        }
        if (keys.includes('shift')) electronKeys.push('Shift');
        if (keys.includes('alt')) electronKeys.push('Alt');

        const mainKey = keys.find(k => !['ctrl', 'cmd', 'shift', 'alt'].includes(k));
        if (mainKey) {
            // Map to Electron accelerator format
            let electronKey = mainKey;
            if (mainKey === 'space') electronKey = 'Space';
            else if (mainKey === 'return') electronKey = 'Return';
            else if (mainKey === 'escape') electronKey = 'Escape';
            else if (mainKey === 'tab') electronKey = 'Tab';
            else if (mainKey === 'backspace') electronKey = 'Backspace';
            else if (mainKey === 'delete') electronKey = 'Delete';
            else if (mainKey.startsWith('f') && mainKey.length <= 3) electronKey = mainKey.toUpperCase();
            else electronKey = mainKey.charAt(0).toUpperCase() + mainKey.slice(1);

            electronKeys.push(electronKey);
        }

        if (electronKeys.length < 2) {
            this.showHotkeyValidation('Hotkey must include at least one modifier key', 'error');
            this.hotkeyDisplay.className = 'hotkey-display error';
            setTimeout(() => this.cancelHotkeyCapture(), 2000);
            return;
        }

        const newHotkey = electronKeys.join('+');

        try {
            // First validate the hotkey
            const validation = await ipcRenderer.invoke('validate-hotkey', newHotkey);
            if (!validation.valid) {
                this.showHotkeyValidation(validation.error, 'error');
                this.hotkeyDisplay.className = 'hotkey-display error';
                setTimeout(() => this.cancelHotkeyCapture(), 2000);
                return;
            }

            // Test if hotkey is available
            const testResult = await ipcRenderer.invoke('test-hotkey', newHotkey);
            if (!testResult.available) {
                this.showHotkeyValidation(testResult.error || 'Hotkey not available', 'error');
                this.hotkeyDisplay.className = 'hotkey-display error';
                setTimeout(() => this.cancelHotkeyCapture(), 2000);
                return;
            }

            // Apply the hotkey
            const result = await ipcRenderer.invoke('update-hotkey', newHotkey);
            if (result.success) {
                this.config.hotkey = newHotkey;
                this.hotkeyDisplay.className = 'hotkey-display success';
                this.showHotkeyValidation('Hotkey updated successfully!', 'success');
                setTimeout(() => {
                    this.updateHotkeyDisplay(newHotkey);
                    this.finishHotkeyCapture();
                }, 1500);
            } else {
                this.showHotkeyValidation(result.error || 'Failed to register hotkey', 'error');
                this.hotkeyDisplay.className = 'hotkey-display error';
                setTimeout(() => this.cancelHotkeyCapture(), 2000);
            }
        } catch (error) {
            console.error('Failed to update hotkey:', error);
            this.showHotkeyValidation('Failed to update hotkey', 'error');
            this.hotkeyDisplay.className = 'hotkey-display error';
            setTimeout(() => this.cancelHotkeyCapture(), 2000);
        }
    }

    cancelHotkeyCapture() {
        this.finishHotkeyCapture();
        this.updateHotkeyDisplay(this.config?.hotkey || 'CommandOrControl+Shift+Space');
    }

    finishHotkeyCapture() {
        this.isCapturingHotkey = false;
        this.capturedKeys.clear();
        if (this.hotkeyTimeout) {
            clearTimeout(this.hotkeyTimeout);
            this.hotkeyTimeout = null;
        }
        this.hotkeyDisplay.className = 'hotkey-display';
    }

    showHotkeyValidation(message, type = 'error') {
        if (!this.hotkeyValidation) return;

        this.hotkeyValidation.textContent = message;
        this.hotkeyValidation.className = `hotkey-validation ${type}`;
    }

    clearHotkeyValidation() {
        if (!this.hotkeyValidation) return;

        this.hotkeyValidation.textContent = '';
        this.hotkeyValidation.className = 'hotkey-validation';
    }

    onHotkeyFallbackUsed(data) {
        console.warn('Hotkey fallback used:', data);
        this.showHotkeyValidation(data.message, 'error');

        // Update the display with the fallback hotkey
        if (data.fallbackHotkey) {
            this.config.hotkey = data.fallbackHotkey;
            this.updateHotkeyDisplay(data.fallbackHotkey);
        }

        // Show a more prominent notification
        this.showError(`Hotkey changed: ${data.message}`);
    }

    onHotkeyRegistrationFailed(data) {
        console.error('Hotkey registration failed:', data);
        this.showHotkeyValidation(data.message, 'error');
        this.showError(`Hotkey Error: ${data.message}`);

        // Offer to reset to default
        setTimeout(() => {
            if (confirm('Would you like to reset the hotkey to the default (Ctrl/Cmd + Shift + Space)?')) {
                this.resetHotkeyToDefault();
            }
        }, 2000);
    }

    async resetHotkeyToDefault() {
        try {
            const result = await ipcRenderer.invoke('reset-hotkey-to-default');
            if (result.success) {
                this.config.hotkey = 'CommandOrControl+Shift+Space';
                this.updateHotkeyDisplay(this.config.hotkey);
                this.showHotkeyValidation('Hotkey reset to default', 'success');
                this.showSuccess('Hotkey reset to default successfully');
            } else {
                this.showHotkeyValidation(result.error || 'Failed to reset hotkey', 'error');
            }
        } catch (error) {
            console.error('Failed to reset hotkey:', error);
            this.showHotkeyValidation('Failed to reset hotkey', 'error');
        }
    }

    async toggleRecording() {
        try {
            if (this.isRecording) {
                await ipcRenderer.invoke('stop-recording');
            } else {
                await ipcRenderer.invoke('start-recording');
            }
        } catch (error) {
            console.error('Failed to toggle recording:', error);
            this.showError('Failed to toggle recording');
        }
    }

    toggleSettings() {
        const configVisible = this.configSection.style.display !== 'none';
        const authVisible = this.authSection.style.display !== 'none';

        if (configVisible || authVisible) {
            // Hide both sections
            this.configSection.style.display = 'none';
            this.authSection.style.display = 'none';
            this.settingsBtn.textContent = 'Settings';
        } else {
            // Show both sections
            this.configSection.style.display = 'block';
            this.authSection.style.display = 'block';
            this.settingsBtn.textContent = 'Hide Settings';
        }
    }

    async saveConfiguration() {
        try {
            const updatedConfig = {
                speechSettings: {
                    ...this.config.speechSettings,
                    language: this.languageSelect.value
                }
            };

            const success = await ipcRenderer.invoke('update-config', updatedConfig);

            if (success) {
                this.showSuccess('Configuration saved successfully');
                this.config = await ipcRenderer.invoke('get-config');
            } else {
                this.showError('Failed to save configuration');
            }
        } catch (error) {
            console.error('Failed to save configuration:', error);
            this.showError('Failed to save configuration');
        }
    }

    // Connection test method (replaces API key validation)
    async testConnection() {
        try {
            const isConnected = await ipcRenderer.invoke('test-connection');

            if (isConnected) {
                this.showSuccess('Backend connection successful');
            } else {
                this.showError('Backend connection failed - please check your authentication');
            }
        } catch (error) {
            console.error('Connection test failed:', error);
            this.showError('Connection test failed');
        }
    }

    onRecordingStarted() {
        this.isRecording = true;
        this.statusDot.classList.add('recording');
        this.statusText.textContent = 'Recording...';
        this.recordBtn.textContent = 'Stop Recording';
        this.recordBtn.classList.remove('btn-primary');
        this.recordBtn.classList.add('btn-secondary');
    }

    onRecordingStopped() {
        this.isRecording = false;
        this.statusDot.classList.remove('recording');
        this.statusText.textContent = 'Processing...';
        this.recordBtn.textContent = 'Start Recording';
        this.recordBtn.classList.remove('btn-secondary');
        this.recordBtn.classList.add('btn-primary');
    }

    onTranscriptionComplete(text) {
        this.statusText.textContent = 'Ready';
        this.transcriptionText.textContent = text;

        // Show a brief success indicator
        this.statusText.textContent = 'Text inserted!';
        setTimeout(() => {
            this.statusText.textContent = 'Ready';
        }, 2000);
    }

    onAIResponseComplete(data) {
        this.statusText.textContent = 'Ready';

        // Display the AI response with mode information
        const displayText = `[${data.mode.toUpperCase()} MODE]\nPrompt: "${data.prompt}"\n\nResponse:\n${data.response}`;
        this.transcriptionText.textContent = displayText;

        // Show success indicator with mode
        this.statusText.textContent = `${data.mode.charAt(0).toUpperCase() + data.mode.slice(1)} response inserted!`;
        setTimeout(() => {
            this.statusText.textContent = 'Ready';
        }, 3000);
    }

    onWindowFocus() {
        // Window gained focus
        console.log('Window focused');
    }

    onWindowBlur() {
        // Window lost focus
        console.log('Window blurred');
    }

    showError(message) {
        // Simple error display - in a real app you'd want a proper notification system
        console.error(message);
        this.statusText.textContent = `Error: ${message}`;
        this.statusText.style.color = '#e53e3e';
        
        setTimeout(() => {
            this.statusText.textContent = 'Ready';
            this.statusText.style.color = '';
        }, 3000);
    }

    showSuccess(message) {
        console.log(message);
        this.statusText.textContent = message;
        this.statusText.style.color = '#38a169';
        
        setTimeout(() => {
            this.statusText.textContent = 'Ready';
            this.statusText.style.color = '';
        }, 2000);
    }
}

// Initialize the renderer app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new RendererApp();
});

// Handle uncaught errors
window.addEventListener('error', (event) => {
    console.error('Renderer error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
});
