<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vibe Typer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(10px);
        }

        .header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #4a5568;
        }

        .header h1 {
            color: #e2e8f0;
            font-size: 24px;
            margin-bottom: 8px;
        }

        .header p {
            color: #a0aec0;
            font-size: 14px;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .status-section {
            background: #2d3748;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            background: #e53e3e;
            transition: background 0.3s ease;
        }

        .status-dot.recording {
            background: #38a169;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .status-text {
            font-weight: 500;
            color: #e2e8f0;
        }

        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: #4299e1;
            color: white;
        }

        .btn-primary:hover {
            background: #3182ce;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #4a5568;
            color: #e2e8f0;
        }

        .btn-secondary:hover {
            background: #718096;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .config-section {
            background: #2d3748;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .config-section h3 {
            color: #e2e8f0;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #e2e8f0;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #4a5568;
            border-radius: 6px;
            font-size: 14px;
            background: #1a202c;
            color: #e2e8f0;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .hotkey-display {
            background: #1a202c;
            border: 1px solid #4a5568;
            border-radius: 6px;
            padding: 12px;
            text-align: center;
            font-family: monospace;
            font-weight: bold;
            color: #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-height: 20px;
        }

        .hotkey-display:hover {
            background: #2d3748;
            border-color: #718096;
        }

        .hotkey-display.capturing {
            background: #fff3cd;
            border-color: #ffeaa7;
            animation: pulse-border 1.5s infinite;
        }

        .hotkey-display.error {
            background: #fed7d7;
            border-color: #feb2b2;
            color: #c53030;
        }

        .hotkey-display.success {
            background: #c6f6d5;
            border-color: #9ae6b4;
            color: #2d7d32;
        }

        @keyframes pulse-border {
            0% { border-color: #ffeaa7; }
            50% { border-color: #f39c12; }
            100% { border-color: #ffeaa7; }
        }

        .hotkey-help {
            font-size: 11px;
            color: #a0aec0;
            margin-top: 8px;
            line-height: 1.4;
        }

        .hotkey-validation {
            font-size: 12px;
            margin-top: 5px;
            min-height: 16px;
        }

        .hotkey-validation.error {
            color: #e53e3e;
        }

        .hotkey-validation.success {
            color: #38a169;
        }

        .hotkey-suggestions {
            margin-top: 10px;
        }

        .hotkey-suggestion {
            display: inline-block;
            background: #4a5568;
            border: 1px solid #718096;
            border-radius: 4px;
            padding: 4px 8px;
            margin: 2px;
            font-size: 11px;
            font-family: monospace;
            color: #e2e8f0;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .hotkey-suggestion:hover {
            background: #718096;
            border-color: #a0aec0;
        }

        .transcription-area {
            background: #1a202c;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .transcription-area h3 {
            color: #e2e8f0;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .transcription-text {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 6px;
            padding: 15px;
            min-height: 100px;
            font-size: 14px;
            line-height: 1.5;
            color: #e2e8f0;
        }

        .footer {
            padding: 15px 20px;
            border-top: 1px solid #4a5568;
            text-align: center;
            font-size: 12px;
            color: #a0aec0;
        }

        .hidden {
            display: none;
        }

        .error {
            color: #e53e3e;
            font-size: 12px;
            margin-top: 5px;
        }

        .success {
            color: #38a169;
            font-size: 12px;
            margin-top: 5px;
        }

        .btn-link {
            background: none;
            border: none;
            color: #4299e1;
            text-decoration: underline;
            cursor: pointer;
            font-size: 12px;
            padding: 5px 0;
        }

        .btn-link:hover {
            color: #3182ce;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        #userInfo {
            text-align: center;
            padding: 15px;
            background: rgba(56, 161, 105, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(56, 161, 105, 0.3);
        }

        .usage-stats {
            font-size: 12px;
            color: #a0aec0;
            margin-top: 10px;
            padding: 10px;
            background: rgba(45, 55, 72, 0.5);
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Vibe Typer</h1>
            <p>AI-powered speech-to-text with global hotkeys</p>
        </div>

        <div class="main-content">
            <!-- Status Section -->
            <div class="status-section">
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span class="status-text" id="statusText">Ready</span>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" id="recordBtn">Start Recording</button>
                    <button class="btn btn-secondary" id="settingsBtn">Settings</button>
                </div>
            </div>

            <!-- Authentication Section -->
            <div class="config-section" id="authSection">
                <h3 id="authTitle">Sign In</h3>

                <!-- Sign In Form -->
                <div id="signInForm">
                    <div class="form-group">
                        <label for="signInEmail">Email:</label>
                        <input type="email" id="signInEmail" placeholder="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="signInPassword">Password:</label>
                        <input type="password" id="signInPassword" placeholder="Password">
                    </div>

                    <div class="form-group">
                        <button class="btn btn-primary" id="signInBtn">Sign In</button>
                        <button class="btn btn-secondary" id="showSignUpBtn">Create Account</button>
                    </div>

                    <div class="form-group">
                        <button class="btn btn-link" id="forgotPasswordBtn">Forgot Password?</button>
                    </div>
                </div>

                <!-- Sign Up Form -->
                <div id="signUpForm" class="hidden">
                    <div class="form-group">
                        <label for="signUpEmail">Email:</label>
                        <input type="email" id="signUpEmail" placeholder="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="signUpPassword">Password:</label>
                        <input type="password" id="signUpPassword" placeholder="Password (min 6 characters)">
                    </div>

                    <div class="form-group">
                        <label for="signUpPasswordConfirm">Confirm Password:</label>
                        <input type="password" id="signUpPasswordConfirm" placeholder="Confirm Password">
                    </div>

                    <div class="form-group">
                        <button class="btn btn-primary" id="signUpBtn">Create Account</button>
                        <button class="btn btn-secondary" id="showSignInBtn">Back to Sign In</button>
                    </div>
                </div>

                <!-- User Info (when signed in) -->
                <div id="userInfo" class="hidden">
                    <div class="form-group">
                        <label>Signed in as:</label>
                        <div id="userEmail" style="color: #38a169; font-weight: bold;"></div>
                    </div>

                    <div class="form-group">
                        <button class="btn btn-secondary" id="signOutBtn">Sign Out</button>
                        <button class="btn btn-link" id="viewUsageBtn">View Usage</button>
                    </div>
                </div>

                <!-- Auth Messages -->
                <div id="authError" class="error hidden"></div>
                <div id="authSuccess" class="success hidden"></div>
            </div>

            <!-- Configuration Section -->
            <div class="config-section" id="configSection">
                <h3>Configuration</h3>

                <div class="form-group">
                    <label for="language">Language:</label>
                    <select id="language">
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="zh">Chinese</option>
                        <option value="ja">Japanese</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Global Hotkey:</label>
                    <div class="hotkey-display" id="hotkeyDisplay">Ctrl/Cmd + Shift + Space</div>
                    <div class="hotkey-validation" id="hotkeyValidation"></div>
                    <div class="hotkey-help">
                        Click to record a new hotkey combination. Must include at least one modifier key (Ctrl, Shift, Alt).
                        <br>Avoid system shortcuts like Ctrl+C, Ctrl+V, etc.
                    </div>
                    <div class="hotkey-suggestions" id="hotkeysuggestions">
                        <div style="font-size: 11px; color: #a0aec0; margin-bottom: 5px;">Quick suggestions:</div>
                    </div>
                    <button class="btn btn-secondary" id="resetHotkeyBtn" style="margin-top: 10px; font-size: 12px; padding: 6px 12px;">Reset to Default</button>
                </div>

                <button class="btn btn-primary" id="saveConfigBtn">Save Configuration</button>
            </div>

            <!-- AI Commands Help -->
            <div class="config-section">
                <h3>AI Voice Commands</h3>
                <div style="font-size: 12px; color: #a0aec0; line-height: 1.4;">
                    <strong>Write Mode:</strong> "Write an answer to the message in my clipboard, and tell them politely that their offer is not as compelling as competing offers"<br><br>
                    <strong>Answer Mode:</strong> "Answer the question: How tall is the Eiffel tower?"<br><br>
                    <strong>Rewrite Mode:</strong> "Rewrite this to Portuguese" or "Rewrite this to bullet points"<br><br>
                    <strong>Reply Mode:</strong> "Reply that we don't offer these services, in a polite way"
                </div>
            </div>

            <!-- Transcription Area -->
            <div class="transcription-area">
                <h3>Last Transcription / AI Response</h3>
                <div class="transcription-text" id="transcriptionText">
                    Transcribed text or AI responses will appear here...
                </div>
            </div>
        </div>

        <div class="footer">
            Press your configured hotkey to toggle recording • Text will be automatically inserted
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
